"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Filter, X } from "lucide-react";
import {
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";
import { ServiceStatus, DeviceType } from "../types";

export interface ServiceFilterState {
  status?: ServiceStatus;
  deviceType?: DeviceType;
  customerName?: string;
  serviceNumber?: string;
  dateRange?: {
    from?: Date;
    to?: Date;
  };
  estimatedCostRange?: {
    min?: number;
    max?: number;
  };
  isDraft?: boolean;
}

interface ServiceFilterProps {
  filters: ServiceFilterState;
  onFilterChange: (filters: ServiceFilterState) => void;
}

export const ServiceFilter: React.FC<ServiceFilterProps> = ({
  filters,
  onFilterChange,
}) => {
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [tempFilters, setTempFilters] = useState<ServiceFilterState>(filters);

  const handleApplyFilters = () => {
    onFilterChange(tempFilters);
    setShowFilterDialog(false);
  };

  const handleResetFilters = () => {
    const resetFilters: ServiceFilterState = {};
    setTempFilters(resetFilters);
    onFilterChange(resetFilters);
    setShowFilterDialog(false);
  };

  const getActiveFilterCount = () => {
    return Object.values(filters).filter((value) => {
      if (value === undefined || value === null || value === "") return false;
      if (typeof value === "object" && Object.keys(value).length === 0)
        return false;
      return true;
    }).length;
  };

  const getStatusLabel = (status: ServiceStatus) => {
    switch (status) {
      case ServiceStatus.DITERIMA:
        return "Diterima";
      case ServiceStatus.PROSES_MENUNGGU_SPAREPART:
        return "Proses/Menunggu Sparepart";
      case ServiceStatus.SELESAI_BELUM_DIAMBIL:
        return "Selesai & Belum Diambil";
      case ServiceStatus.SELESAI_SUDAH_DIAMBIL:
        return "Selesai & Sudah Diambil";
      default:
        return status;
    }
  };

  const getDeviceTypeLabel = (deviceType: DeviceType) => {
    switch (deviceType) {
      case DeviceType.LAPTOP:
        return "Laptop";
      case DeviceType.DESKTOP:
        return "Desktop";
      case DeviceType.PHONE:
        return "Handphone";
      case DeviceType.TABLET:
        return "Tablet";
      case DeviceType.PRINTER:
        return "Printer";
      case DeviceType.OTHER:
        return "Lainnya";
      default:
        return deviceType;
    }
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dialog open={showFilterDialog} onOpenChange={setShowFilterDialog}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer relative"
        >
          <FunnelIcon className="h-5 w-5" />
          Filter
          {activeFilterCount > 0 && (
            <Badge
              variant="destructive"
              className="ml-2 h-5 w-5 rounded-full p-0 text-xs flex items-center justify-center"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle className="flex items-center gap-2">
            <AdjustmentsHorizontalIcon className="h-5 w-5" />
            Filter Data Servis
          </DialogTitle>
          <DialogDescription>
            Gunakan filter untuk mempersempit pencarian data servis
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 p-2">
          {/* Status Filter */}
          <div className="space-y-2">
            <Label>Status Servis</Label>
            <Select
              value={tempFilters.status || "all"}
              onValueChange={(value) =>
                setTempFilters({
                  ...tempFilters,
                  status:
                    value === "all" ? undefined : (value as ServiceStatus),
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Status</SelectItem>
                <SelectItem value={ServiceStatus.DITERIMA}>
                  {getStatusLabel(ServiceStatus.DITERIMA)}
                </SelectItem>
                <SelectItem value={ServiceStatus.PROSES_MENUNGGU_SPAREPART}>
                  {getStatusLabel(ServiceStatus.PROSES_MENUNGGU_SPAREPART)}
                </SelectItem>
                <SelectItem value={ServiceStatus.SELESAI_BELUM_DIAMBIL}>
                  {getStatusLabel(ServiceStatus.SELESAI_BELUM_DIAMBIL)}
                </SelectItem>
                <SelectItem value={ServiceStatus.SELESAI_SUDAH_DIAMBIL}>
                  {getStatusLabel(ServiceStatus.SELESAI_SUDAH_DIAMBIL)}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Device Type Filter */}
          <div className="space-y-2">
            <Label>Jenis Perangkat</Label>
            <Select
              value={tempFilters.deviceType || "all"}
              onValueChange={(value) =>
                setTempFilters({
                  ...tempFilters,
                  deviceType:
                    value === "all" ? undefined : (value as DeviceType),
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih jenis perangkat" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Jenis</SelectItem>
                <SelectItem value={DeviceType.LAPTOP}>
                  {getDeviceTypeLabel(DeviceType.LAPTOP)}
                </SelectItem>
                <SelectItem value={DeviceType.DESKTOP}>
                  {getDeviceTypeLabel(DeviceType.DESKTOP)}
                </SelectItem>
                <SelectItem value={DeviceType.PHONE}>
                  {getDeviceTypeLabel(DeviceType.PHONE)}
                </SelectItem>
                <SelectItem value={DeviceType.TABLET}>
                  {getDeviceTypeLabel(DeviceType.TABLET)}
                </SelectItem>
                <SelectItem value={DeviceType.PRINTER}>
                  {getDeviceTypeLabel(DeviceType.PRINTER)}
                </SelectItem>
                <SelectItem value={DeviceType.OTHER}>
                  {getDeviceTypeLabel(DeviceType.OTHER)}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Customer Name Filter */}
          <div className="space-y-2">
            <Label>Nama Pelanggan</Label>
            <Input
              placeholder="Cari berdasarkan nama pelanggan"
              value={tempFilters.customerName || ""}
              onChange={(e) =>
                setTempFilters({
                  ...tempFilters,
                  customerName: e.target.value || undefined,
                })
              }
            />
          </div>

          {/* Service Number Filter */}
          <div className="space-y-2">
            <Label>Nomor Servis</Label>
            <Input
              placeholder="Cari berdasarkan nomor servis"
              value={tempFilters.serviceNumber || ""}
              onChange={(e) =>
                setTempFilters({
                  ...tempFilters,
                  serviceNumber: e.target.value || undefined,
                })
              }
            />
          </div>

          {/* Estimated Cost Range */}
          <div className="space-y-2">
            <Label>Rentang Estimasi Biaya</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                type="number"
                placeholder="Min"
                value={tempFilters.estimatedCostRange?.min || ""}
                onChange={(e) =>
                  setTempFilters({
                    ...tempFilters,
                    estimatedCostRange: {
                      ...tempFilters.estimatedCostRange,
                      min: e.target.value ? Number(e.target.value) : undefined,
                    },
                  })
                }
              />
              <Input
                type="number"
                placeholder="Max"
                value={tempFilters.estimatedCostRange?.max || ""}
                onChange={(e) =>
                  setTempFilters({
                    ...tempFilters,
                    estimatedCostRange: {
                      ...tempFilters.estimatedCostRange,
                      max: e.target.value ? Number(e.target.value) : undefined,
                    },
                  })
                }
              />
            </div>
          </div>

          {/* Draft Filter */}
          <div className="space-y-2">
            <Label>Status Draft</Label>
            <Select
              value={
                tempFilters.isDraft === undefined
                  ? "all"
                  : tempFilters.isDraft
                    ? "true"
                    : "false"
              }
              onValueChange={(value) =>
                setTempFilters({
                  ...tempFilters,
                  isDraft:
                    value === "all"
                      ? undefined
                      : value === "true"
                        ? true
                        : false,
                })
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih status draft" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua</SelectItem>
                <SelectItem value="true">Draft</SelectItem>
                <SelectItem value="false">Bukan Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
          <Button
            variant="outline"
            onClick={handleResetFilters}
            className="cursor-pointer"
          >
            <XMarkIcon className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleApplyFilters} className="cursor-pointer">
            <FunnelIcon className="h-4 w-4 mr-2" />
            Terapkan Filter
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
