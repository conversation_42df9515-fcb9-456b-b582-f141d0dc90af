"use client";

import type { NextPage } from "next";
import Head from "next/head";
import DashboardLayout from "@/components/layout/dashboardlayout";
import {
  ExclamationCircleIcon,
  XCircleIcon,
  CheckCircleIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ArrowsUpDownIcon,
} from "@heroicons/react/24/outline";
import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Pagination } from "@/components/ui/pagination";

// Import types and components
import type { Product, StockCounts, Category, ColumnVisibility } from "./types";
import { StockSummaryCards } from "./components/StockSummaryCards";
import { ProductActions } from "./components/ProductActions";
import { ProductTableDesktop } from "./components/ProductTableDesktop";
import { DraftTabContent } from "./components/DraftTabContent";
import { ProductFilterState } from "./components/ProductFilter";
import Link from "next/link";

interface ProductsPageProps {
  products: Product[];
  stockCounts: StockCounts;
  categories: Category[]; // Keep categories if needed for filtering/display elsewhere
}

const ProductsPage: NextPage<ProductsPageProps> = (props) => {
  const { products, stockCounts, categories } = props;
  const router = useRouter();
  const searchParams = useSearchParams();
  const [searchTerm, setSearchTerm] = useState("");

  // Initialize subTab from URL parameter or default to "all-products"
  const [subTab, setSubTab] = useState(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "drafts" || tabParam === "draft-produk") {
      return "drafts";
    } else if (tabParam === "all-products" || tabParam === "daftar-produk") {
      return "all-products";
    }
    return "all-products";
  });

  const [filteredProducts, setFilteredProducts] = useState<Product[]>(products); // Initialize with products

  // Filter state
  const [filters, setFilters] = useState<ProductFilterState>({
    category: undefined,
    stockStatus: "all",
    priceRange: { min: undefined, max: undefined },
    tags: [],
    hasVariants: undefined,
    isDraft: undefined,
  });

  // Update activeTab when URL changes
  useEffect(() => {
    const tabParam = searchParams.get("tab");
    if (tabParam === "drafts" || tabParam === "draft-produk") {
      setSubTab("drafts");
    } else if (tabParam === "all-products" || tabParam === "daftar-produk") {
      setSubTab("all-products");
    }
  }, [searchParams]);

  // DISABLED: Handle tab change and update URL - causing POST requests during navigation
  const handleTabChange = (value: string) => {
    setSubTab(value);

    // DISABLED: URL updates that were causing POST requests during navigation
    // const currentPath = window.location.pathname;
    // let newUrl = currentPath;

    // if (value === "drafts") {
    //   newUrl = `${currentPath}?tab=draft-produk`;
    // } else if (value === "all-products") {
    //   newUrl = `${currentPath}?tab=daftar-produk`;
    // } else {
    //   newUrl = currentPath; // fallback to no parameters
    // }

    // Use router.replace to update URL without refresh
    // router.replace(newUrl, { scroll: false });
    console.log(`Tab changed to: ${value} (URL update disabled)`);
  };

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [paginatedProducts, setPaginatedProducts] = useState<Product[]>([]);

  // Helper function to get default column visibility
  const getDefaultColumnVisibility = (): ColumnVisibility => {
    return {
      image: true,
      name: true,
      sku: true,
      barcode: true,
      category: true,
      tags: true,
      colorVariants: true,
      price: true,
      stock: true,
      stockStatus: true,
      cost: true,
      sellPrice: true,
      wholesalePrice: true,
      discountPrice: true,
      unit: true,
    };
  };

  // Column visibility state with localStorage persistence
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(
    () => {
      // Try to get saved column visibility from localStorage
      if (typeof window !== "undefined") {
        try {
          const savedVisibility = localStorage.getItem(
            "productColumnVisibility"
          );
          if (savedVisibility) {
            const parsed = JSON.parse(savedVisibility) as ColumnVisibility;

            // Validate that all required keys exist in the saved data
            const defaultVisibility = getDefaultColumnVisibility();
            const hasAllKeys = Object.keys(defaultVisibility).every(
              (key) => key in parsed
            );

            if (hasAllKeys) {
              console.log(
                "Using saved product column visibility from localStorage:",
                parsed
              );
              return parsed;
            } else {
              // If saved data is incomplete, remove it and use defaults
              localStorage.removeItem("productColumnVisibility");
              console.warn(
                "Incomplete product column visibility data found, using defaults"
              );
            }
          }
        } catch (error) {
          console.error(
            "Failed to parse saved product column visibility:",
            error
          );
          // Clear corrupted data
          localStorage.removeItem("productColumnVisibility");
        }
      }

      // Default column visibility for new users or when localStorage is invalid
      const defaultVisibility = getDefaultColumnVisibility();
      console.log(
        "Using default product column visibility for new user:",
        defaultVisibility
      );
      return defaultVisibility;
    }
  );

  // Sorting state
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Function to handle column sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      // If already sorting by this field, toggle direction
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // If sorting by a new field, set it and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Function to get sort icon
  const getSortIcon = (field: string) => {
    if (sortField !== field) {
      return <ArrowsUpDownIcon className="h-4 w-4 ml-1 opacity-50" />;
    }
    return sortDirection === "asc" ? (
      <ChevronUpIcon className="h-4 w-4 ml-1" />
    ) : (
      <ChevronDownIcon className="h-4 w-4 ml-1" />
    );
  };

  // Extract available tags from products
  const availableTags = React.useMemo(() => {
    const tagSet = new Set<string>();
    products.forEach((product) => {
      if (product.tags && Array.isArray(product.tags)) {
        product.tags.forEach((tag) => {
          // Handle both string tags and Tag objects
          const tagName = typeof tag === "string" ? tag : tag.name;
          tagSet.add(tagName);
        });
      }
    });
    return Array.from(tagSet).sort();
  }, [products]);

  // Filter and sort products based on search term, active tabs, filters, and sort settings
  useEffect(() => {
    let result = [...products]; // Create a copy to avoid mutating the original

    // Reset to first page when filters change
    setCurrentPage(1);

    // Apply search filter
    if (searchTerm) {
      result = result.filter(
        (product) =>
          product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          product.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (product.sku &&
            product.sku.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply advanced filters
    if (filters.category) {
      result = result.filter(
        (product) => product.categoryId === filters.category
      );
    }

    if (filters.stockStatus && filters.stockStatus !== "all") {
      switch (filters.stockStatus) {
        case "in-stock":
          result = result.filter((product) => product.stock > 5);
          break;
        case "low-stock":
          result = result.filter(
            (product) => product.stock > 0 && product.stock <= 5
          );
          break;
        case "out-of-stock":
          result = result.filter((product) => product.stock === 0);
          break;
      }
    }

    if (filters.priceRange?.min !== undefined) {
      result = result.filter(
        (product) => product.price >= filters.priceRange!.min!
      );
    }

    if (filters.priceRange?.max !== undefined) {
      result = result.filter(
        (product) => product.price <= filters.priceRange!.max!
      );
    }

    if (filters.tags && filters.tags.length > 0) {
      result = result.filter((product) => {
        if (!product.tags || !Array.isArray(product.tags)) return false;
        return filters.tags!.some((filterTag) =>
          product.tags!.some((productTag) => {
            // Handle both string tags and Tag objects
            const productTagName =
              typeof productTag === "string" ? productTag : productTag.name;
            return productTagName === filterTag;
          })
        );
      });
    }

    if (filters.hasVariants !== undefined) {
      result = result.filter(
        (product) => product.hasVariants === filters.hasVariants
      );
    }

    if (filters.isDraft !== undefined) {
      result = result.filter((product) => product.isDraft === filters.isDraft);
    }

    // Apply tab filters - only products tab is handled here
    // Warehouse tab is now handled by a separate page

    // Apply sub-tab filters
    if (subTab === "all-products") {
      // Filter out draft products from all-products tab
      result = result.filter((product) => !product.isDraft);
    } else if (subTab === "drafts") {
      // Show only draft products
      result = result.filter((product) => product.isDraft);
    } else if (subTab === "needs-approval") {
      // In a real implementation, you would filter by approval status
      result = [];
    } else if (subTab === "low-stock") {
      result = result.filter(
        (product) => product.stock > 0 && product.stock <= 5
      );
    } else if (subTab === "out-of-stock") {
      result = result.filter((product) => product.stock === 0);
    }

    // Apply sorting if a sort field is selected
    if (sortField) {
      result.sort((a, b) => {
        let valueA, valueB;

        // Handle different field types
        switch (sortField) {
          case "name":
            valueA = a.name.toLowerCase();
            valueB = b.name.toLowerCase();
            break;
          case "sku":
            valueA = (a.sku || "").toLowerCase();
            valueB = (b.sku || "").toLowerCase();
            break;
          case "barcode":
            valueA = (a.barcode || "").toLowerCase();
            valueB = (b.barcode || "").toLowerCase();
            break;
          case "category":
            valueA = (a.category?.name || "").toLowerCase();
            valueB = (b.category?.name || "").toLowerCase();
            break;
          case "tags":
            valueA =
              a.tags && a.tags.length > 0
                ? a.tags
                    .map((t) => (typeof t === "string" ? t : t.name))
                    .join(", ")
                    .toLowerCase()
                : "";
            valueB =
              b.tags && b.tags.length > 0
                ? b.tags
                    .map((t) => (typeof t === "string" ? t : t.name))
                    .join(", ")
                    .toLowerCase()
                : "";
            break;
          case "price":
            valueA = a.price;
            valueB = b.price;
            break;
          case "stock":
            valueA = a.stock;
            valueB = b.stock;
            break;
          case "cost":
            valueA = a.cost || 0;
            valueB = b.cost || 0;
            break;
          case "wholesalePrice":
            valueA = a.wholesalePrice || 0;
            valueB = b.wholesalePrice || 0;
            break;
          default:
            valueA = a[sortField as keyof Product] || "";
            valueB = b[sortField as keyof Product] || "";
        }

        // Compare based on direction
        if (sortDirection === "asc") {
          return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
        } else {
          return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;
        }
      });
    }

    setFilteredProducts(result);
  }, [products, searchTerm, subTab, sortField, sortDirection, filters]);

  // Apply pagination to filtered products
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedProducts(filteredProducts.slice(startIndex, endIndex));
  }, [filteredProducts, currentPage, itemsPerPage]);

  // Save column visibility to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(
          "productColumnVisibility",
          JSON.stringify(columnVisibility)
        );
      } catch (error) {
        console.error(
          "Failed to save product column visibility to localStorage:",
          error
        );
      }
    }
  }, [columnVisibility]);

  // DISABLED: Refresh function that was causing navigation issues
  const handleRefresh = () => {
    // DISABLED: This was causing POST requests during navigation
    // window.location.reload();

    // Alternative: Show a message instead of reloading
    console.log("Refresh disabled to prevent navigation interference");
  };

  // Function to get stock status badge
  const getStockStatusBadge = (stock: number) => {
    if (stock === 0) {
      return (
        <Badge variant="destructive" className="whitespace-nowrap">
          <XCircleIcon className="h-3 w-3 mr-1 text-white" />
          <span className="text-white">Habis</span>
        </Badge>
      );
    } else if (stock <= 5) {
      return (
        <Badge variant="default" className="bg-amber-500 whitespace-nowrap">
          <ExclamationCircleIcon className="h-3 w-3 mr-1" />
          Segera Habis
        </Badge>
      );
    } else {
      return (
        <Badge variant="default" className="bg-green-500 whitespace-nowrap">
          <CheckCircleIcon className="h-3 w-3 mr-1" />
          Tersedia
        </Badge>
      );
    }
  };

  return (
    <DashboardLayout>
      <Head>
        <title>Produk - Kasir Online</title>
      </Head>

      <div className="space-y-6">
        {/* Main Tabs with URL routing */}
        <Tabs defaultValue="products" value="products" className="w-full">
          <TabsList className="mb-4 w-full md:w-fit">
            <TabsTrigger value="products" asChild>
              <Link href="/dashboard/products">Produk / Jasa</Link>
            </TabsTrigger>
            <TabsTrigger value="warehouse" asChild>
              <Link href="/dashboard/products/warehouse">Gudang</Link>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="products" className="space-y-6">
            {/* Stock Status Summary Cards */}
            <StockSummaryCards stockCounts={stockCounts} />

            {/* Sub Tabs */}
            <Tabs
              defaultValue="all-products"
              value={subTab}
              onValueChange={handleTabChange}
              className="w-full"
            >
              <TabsList className="mb-4 w-full md:w-fit">
                <TabsTrigger value="all-products">Daftar Produk</TabsTrigger>
                <TabsTrigger value="drafts">Draft Produk</TabsTrigger>
              </TabsList>

              <TabsContent value="all-products" className="space-y-6">
                {/* Header Actions */}
                <ProductActions
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  filters={filters}
                  onFilterChange={setFilters}
                  categories={categories.map((cat) => ({
                    id: cat.id,
                    name: cat.name,
                  }))}
                  availableTags={availableTags}
                  onRefresh={handleRefresh}
                />

                {/* Products List */}
                <div className="overflow-x-auto">
                  {/* Table View */}
                  <ProductTableDesktop
                    products={paginatedProducts}
                    columnVisibility={columnVisibility}
                    handleSort={handleSort}
                    getSortIcon={getSortIcon}
                    getStockStatusBadge={getStockStatusBadge}
                    searchTerm={searchTerm}
                  />
                </div>

                {/* Pagination - Moved outside the overflow container */}
                <div className="mt-4">
                  <Pagination
                    currentPage={currentPage}
                    totalPages={Math.ceil(
                      filteredProducts.length / itemsPerPage
                    )}
                    onPageChange={setCurrentPage}
                    itemsPerPage={itemsPerPage}
                    onItemsPerPageChange={setItemsPerPage}
                    totalItems={filteredProducts.length}
                  />
                </div>
              </TabsContent>

              <TabsContent value="drafts" className="space-y-6">
                {/* Header Actions */}
                <ProductActions
                  columnVisibility={columnVisibility}
                  setColumnVisibility={setColumnVisibility}
                  searchTerm={searchTerm}
                  setSearchTerm={setSearchTerm}
                  filters={filters}
                  onFilterChange={setFilters}
                  categories={categories.map((cat) => ({
                    id: cat.id,
                    name: cat.name,
                  }))}
                  availableTags={availableTags}
                  onRefresh={handleRefresh}
                />

                {/* Products List */}
                {filteredProducts.filter((p) => p.isDraft).length > 0 ? (
                  <div className="overflow-x-auto">
                    {/* Table View */}
                    <ProductTableDesktop
                      products={paginatedProducts.filter((p) => p.isDraft)}
                      columnVisibility={columnVisibility}
                      handleSort={handleSort}
                      getSortIcon={getSortIcon}
                      getStockStatusBadge={getStockStatusBadge}
                      searchTerm={searchTerm}
                    />
                  </div>
                ) : (
                  <DraftTabContent />
                )}

                {/* Pagination - Moved outside the overflow container */}
                {filteredProducts.filter((p) => p.isDraft).length > 0 && (
                  <div className="mt-4">
                    <Pagination
                      currentPage={currentPage}
                      totalPages={Math.ceil(
                        filteredProducts.filter((p) => p.isDraft).length /
                          itemsPerPage
                      )}
                      onPageChange={setCurrentPage}
                      itemsPerPage={itemsPerPage}
                      onItemsPerPageChange={setItemsPerPage}
                      totalItems={
                        filteredProducts.filter((p) => p.isDraft).length
                      }
                    />
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ProductsPage;
