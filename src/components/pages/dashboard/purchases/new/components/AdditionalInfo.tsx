"use client";

import React, { useState, useRef } from "react";
import { Control, useFormContext } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { PurchaseFormValues } from "../types";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Upload,
  X,
  File,
  Image as ImageIcon,
  Paperclip,
  Check,
} from "lucide-react";
import { toast } from "sonner";
import {
  deletePurchaseDocument,
  uploadPurchaseDocument,
} from "@/actions/uploads/documents";
import DiscountSection from "./DiscountSection";

interface AdditionalInfoProps {
  control: Control<PurchaseFormValues>;
  isPending: boolean;
  items?: PurchaseFormValues["items"];
}

const AdditionalInfo: React.FC<AdditionalInfoProps> = ({
  control,
  isPending,
  items = [],
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [fileInputKey, setFileInputKey] = useState<number>(Date.now());
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const form = useFormContext();

  // Get the priceIncludesTax flag and discount values
  const priceIncludesTax = form.watch("priceIncludesTax");
  const paymentStatus = form.watch("paymentStatus");
  const globalDiscountPercentage = form.watch("globalDiscountPercentage") || 0;
  const globalDiscountAmount = form.watch("globalDiscountAmount") || 0;

  // Calculate subtotal before discount
  const subtotalBeforeDiscount = items.reduce((sum, item) => {
    const quantity = item?.quantity ?? 0;
    const cost = item?.costAtPurchase ?? 0;
    return sum + quantity * cost;
  }, 0);

  // Calculate global discount
  const globalDiscount =
    globalDiscountPercentage > 0
      ? subtotalBeforeDiscount * (globalDiscountPercentage / 100)
      : globalDiscountAmount;

  // Calculate subtotal after discount
  const subtotalAfterDiscount = Math.max(
    0,
    subtotalBeforeDiscount - globalDiscount
  );

  // Calculate tax amount based on discounted amount
  let taxAmount = 0;
  let calculatedTotal = 0;

  if (priceIncludesTax) {
    // If price includes tax, the total is the discounted amount
    calculatedTotal = subtotalAfterDiscount;

    // Extract tax from the discounted price
    taxAmount = items.reduce((sum, item) => {
      const quantity = item?.quantity ?? 0;
      const cost = item?.costAtPurchase ?? 0;
      const taxRate = parseFloat(item?.tax || "0") / 100;

      // Calculate item's proportion of the total before discount
      const itemProportion =
        subtotalBeforeDiscount > 0
          ? (quantity * cost) / subtotalBeforeDiscount
          : 0;

      // Apply discount proportionally to this item
      const itemAfterDiscount =
        quantity * cost - globalDiscount * itemProportion;

      // Extract tax from the discounted price: total - (total / (1 + taxRate))
      const itemTax = itemAfterDiscount - itemAfterDiscount / (1 + taxRate);
      return sum + itemTax;
    }, 0);
  } else {
    // If price doesn't include tax, add tax to the discounted amount
    taxAmount = items.reduce((sum, item) => {
      const quantity = item?.quantity ?? 0;
      const cost = item?.costAtPurchase ?? 0;
      const taxRate = parseFloat(item?.tax || "0") / 100;

      // Calculate item's proportion of the total before discount
      const itemProportion =
        subtotalBeforeDiscount > 0
          ? (quantity * cost) / subtotalBeforeDiscount
          : 0;

      // Apply discount proportionally to this item
      const itemAfterDiscount =
        quantity * cost - globalDiscount * itemProportion;

      // Calculate tax on the discounted amount
      return sum + itemAfterDiscount * taxRate;
    }, 0);

    calculatedTotal = subtotalAfterDiscount + taxAmount;
  }

  // For display purposes, show subtotal before tax but after discount
  const subtotalBeforeTax = priceIncludesTax
    ? calculatedTotal - taxAmount
    : subtotalAfterDiscount;

  // Calculate remaining payment (sisa tagihan)
  const sisaTagihan = paymentStatus === "paid" ? 0 : calculatedTotal;

  // Function to get icon based on file extension
  const getFileIcon = (filename: string) => {
    const extension = filename.split(".").pop()?.toLowerCase();

    if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension || "")) {
      return <ImageIcon className="h-4 w-4 text-blue-500" aria-hidden="true" />;
    } else if (["pdf", "doc", "docx", "txt"].includes(extension || "")) {
      return <FileText className="h-4 w-4 text-red-500" />;
    } else {
      return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      toast.error("Ukuran file tidak boleh lebih dari 10MB");
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadPurchaseDocument(formData);

      if (result.success && result.url) {
        // Get the current lampiran array from the form
        const currentLampiran = form.getValues("lampiran") || [];

        // Store both the URL and the original filename
        const newAttachment = {
          url: result.url,
          filename: result.filename || file.name,
        };

        // Update the form with the new attachment
        form.setValue("lampiran", [...currentLampiran, newAttachment], {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        toast.success("Dokumen berhasil diunggah");
      } else {
        toast.error(result.error || "Gagal mengunggah dokumen");
      }
    } catch (error) {
      console.error("Upload Error:", error);
      toast.error("Gagal mengunggah dokumen");
    } finally {
      setIsUploading(false);
      // Reset the file input by updating the key
      setFileInputKey(Date.now());
    }
  };

  // Handle file deletion
  const handleDeleteFile = async (index: number, url: string) => {
    try {
      const result = await deletePurchaseDocument(url);

      if (result.success) {
        // Get the current lampiran array from the form
        const currentLampiran = form.getValues("lampiran") || [];

        // Remove the file at the specified index
        const newLampiran = [...currentLampiran];
        newLampiran.splice(index, 1);

        // Update the form
        form.setValue("lampiran", newLampiran, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        toast.success("Dokumen berhasil dihapus");
      } else {
        toast.error(result.error || "Gagal menghapus dokumen");
      }
    } catch (error) {
      console.error("Delete Error:", error);
      toast.error("Gagal menghapus dokumen");
    }
  };
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Left Column - Memo and Lampiran */}
      <div className="space-y-4">
        {/* Memo Field */}
        <div>
          <div className="flex items-center gap-1.5 mb-1">
            <FileText className="h-4 w-4 text-purple-600" />
            <h4 className="text-sm font-medium">Memo</h4>
          </div>
          <FormField
            control={control}
            name="memo"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Textarea
                    placeholder="Tambahkan memo untuk pembelian ini..."
                    className="resize-none h-[80px]"
                    {...field}
                    disabled={isPending}
                    maxLength={1000}
                  />
                </FormControl>
                <FormDescription className="text-xs mt-1 flex justify-between">
                  <span>Memo internal untuk referensi</span>
                  <span className="text-muted-foreground">
                    {field.value?.length || 0}/1000
                  </span>
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Lampiran Field */}
        <div>
          <div className="flex items-center gap-1.5 mb-1">
            <Paperclip className="h-4 w-4 text-amber-600" />
            <h4 className="text-sm font-medium">Lampiran</h4>
          </div>
          <div className="border border-dashed rounded-lg p-2 flex flex-row items-center justify-between bg-muted/30">
            <input
              type="file"
              ref={fileInputRef}
              key={fileInputKey}
              className="hidden"
              onChange={handleFileUpload}
              accept="image/*,.pdf,.doc,.docx,.txt"
            />
            <div className="flex items-center">
              <Upload className="h-5 w-5 text-muted-foreground mr-2" />
              <div className="text-left">
                <p className="text-xs">Unggah dokumen</p>
                <p className="text-xs text-muted-foreground">
                  PDF, gambar, atau dokumen (max 10MB)
                </p>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              className="cursor-pointer h-8"
              disabled={isPending || isUploading}
              onClick={() => fileInputRef.current?.click()}
              size="sm"
            >
              Pilih File
            </Button>
          </div>

          {/* Display uploaded files */}
          <FormField
            control={control}
            name="lampiran"
            render={({ field }) => (
              <FormItem className="w-full">
                {field.value && field.value.length > 0 ? (
                  <div className="w-full">
                    <ul className="space-y-1 text-left mt-2">
                      {field.value.map(
                        (
                          file: { url: string; filename: string },
                          index: number
                        ) => {
                          return (
                            <li
                              key={index}
                              className="flex items-center justify-between bg-muted p-1 rounded-md text-xs"
                            >
                              <div className="flex items-center gap-1.5">
                                {getFileIcon(file.filename)}
                                <span className="truncate max-w-[180px]">
                                  {file.filename}
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  handleDeleteFile(index, file.url);
                                }}
                                disabled={isPending || isUploading}
                                className="cursor-pointer h-6 w-6 p-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </li>
                          );
                        }
                      )}
                    </ul>
                  </div>
                ) : null}
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Right Column - Payment Summary */}
      <div className="h-full flex flex-col">
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 h-full flex flex-col justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 underline italic">
              Ringkasan Pembelian
            </h4>
            <div className="flex justify-between items-center py-3">
              <span className="text-sm font-medium">Sub-total:</span>
              <span className="font-medium">
                Rp{" "}
                {subtotalBeforeTax.toLocaleString("id-ID", {
                  minimumFractionDigits: 0,
                })}
              </span>
            </div>

            {/* Discount Section - placed under subtotal */}
            <DiscountSection
              control={control}
              isPending={isPending}
              items={items}
            />
            {globalDiscount > 0 && (
              <>
                <div className="border-t border-gray-200 dark:border-gray-700"></div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-green-600">
                    Diskon:
                  </span>
                  <span className="font-medium text-green-600">
                    -Rp{" "}
                    {globalDiscount.toLocaleString("id-ID", {
                      minimumFractionDigits: 0,
                    })}
                  </span>
                </div>
              </>
            )}
            {taxAmount > 0 && (
              <>
                <div className="border-t border-gray-200 dark:border-gray-700"></div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium">Pajak:</span>
                  <span className="font-medium">
                    Rp{" "}
                    {taxAmount.toLocaleString("id-ID", {
                      minimumFractionDigits: 0,
                    })}
                  </span>
                </div>
              </>
            )}
            <div className="border-t border-gray-200 dark:border-gray-700"></div>
            <div className="flex justify-between items-center py-3">
              <span className="text-sm font-medium">Total:</span>
              <span className="font-medium">
                Rp{" "}
                {calculatedTotal.toLocaleString("id-ID", {
                  minimumFractionDigits: 0,
                })}
              </span>
            </div>

            {/* Tax inclusion indicator */}
            <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              {priceIncludesTax
                ? "Harga sudah termasuk pajak"
                : "Harga belum termasuk pajak"}
            </div>
            {/* <div className="border-t border-gray-200 dark:border-gray-700"></div>
            <div className="flex justify-between items-center py-3">
              <span className="text-sm font-medium">Sisa tagihan:</span>
              <span className="font-medium text-red-600">
                Rp{" "}
                {sisaTagihan.toLocaleString("id-ID", {
                  minimumFractionDigits: 0,
                })}
              </span>
            </div> */}
          </div>
          {/* <div className="flex justify-end mt-4">
            {paymentStatus === "paid" && (
              <Badge className="bg-green-500 text-white rounded-full px-3">
                <Check className="h-3 w-3 mr-1" />
                Lunas
              </Badge>
            )}
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfo;
