"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  addSale,
  getNextTransactionNumber,
  getNextInvoiceNumber,
} from "@/actions/entities/sales";
import { getActiveEventDiscounts } from "@/actions/event-discounts";
import { EventDiscount } from "@/components/pages/dashboard/event-discounts/types";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { SaleFormValues, Product, EnhancedSaleSchema } from "./types";
import CombinedSaleForm from "./components/CombinedSaleForm";
import { StockStatusCard } from "@/components/ui/stock-status-card";
import { ArrowLeft, Check, Save, AlertCircle } from "lucide-react";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";
import { useTransactionLimits } from "@/hooks/useSubscriptionLimits";
import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Props use imported types
interface EnhancedSalePageProps {
  products: Product[];
  availableCount?: number;
  outOfStockCount?: number;
}

const EnhancedSalePage: React.FC<EnhancedSalePageProps> = ({
  products,
  availableCount = 0,
  outOfStockCount = 0,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(0);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [activeEventDiscounts, setActiveEventDiscounts] = useState<
    EventDiscount[]
  >([]);

  const {
    canCreateTransaction,
    transactionMessage,
    currentTransactionUsage,
    transactionLimit,
    isLoading: limitsLoading,
  } = useTransactionLimits();

  // Initialize the form with enhanced schema
  const form = useForm<SaleFormValues>({
    resolver: zodResolver(EnhancedSaleSchema),
    defaultValues: {
      items: [
        {
          productId: "",
          quantity: 1,
          priceAtSale: 0,
          discount: "0",
          unit: "Buah",
          tax: "",
          isWholesale: false,
        },
      ],
      totalAmount: 0,
      customerId: "", // No default customer
      customerEmail: "",
      customerPhone: "",
      customerNIK: "",
      customerNPWP: "",
      paymentMethod: "cash",
      amountPaid: 0,
      notes: "",
      // New fields
      transactionDate: new Date(),
      transactionNumber: "",
      invoiceRef: "",
      customerRefNumber: "",
      shippingAddress: "",
      paymentDueDate: undefined,
      paymentTerms: "Net 30",
      warehouse: "",
      tags: [],
      isDraft: false,
      memo: "",
      lampiran: [],
      priceIncludesTax: false,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");
  const formValues = form.watch();
  const totalAmountValue = form.watch("totalAmount");

  // Calculate total amount whenever items change
  useEffect(() => {
    const total = items.reduce(
      (sum: number, item: SaleFormValues["items"][number]) => {
        const quantity = item?.quantity ?? 0;
        const price = item?.priceAtSale ?? 0;
        return sum + quantity * price;
      },
      0
    );
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Update totalAmount state when form value changes
  useEffect(() => {
    setTotalAmount(totalAmountValue);
  }, [totalAmountValue]);

  // Check for unsaved changes
  useEffect(() => {
    // Consider form has unsaved changes if any meaningful data is entered
    const hasChanges = !!(
      formValues.items.some((item) => item.productId) ||
      formValues.customerId !== "default" ||
      formValues.customerEmail ||
      formValues.customerRefNumber ||
      formValues.shippingAddress ||
      (formValues.tags && formValues.tags.length > 0)
    );
    setHasUnsavedChanges(hasChanges);
  }, [formValues]);

  // Fetch active event discounts
  useEffect(() => {
    const fetchEventDiscounts = async () => {
      try {
        const result = await getActiveEventDiscounts();
        if (result.success && result.data) {
          setActiveEventDiscounts(result.data);
        }
      } catch (error) {
        console.error("Error fetching event discounts:", error);
      }
    };

    fetchEventDiscounts();
  }, []);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  // Handle product selection with stock validation
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );

    // Check if selectedProduct exists and its price is a number
    if (selectedProduct && typeof selectedProduct.price === "number") {
      // Check stock availability
      if (selectedProduct.stock <= 0) {
        toast.error(
          `Produk "${selectedProduct.name}" sedang habis stok dan tidak dapat dijual.`
        );
        // Clear the selection
        form.setValue(`items.${index}.productId`, "");
        return;
      }

      // Get current wholesale setting for this item
      const currentItems = form.getValues("items");
      const isWholesale = currentItems[index]?.isWholesale || false;

      // Determine price based on wholesale setting
      const priceValue = isWholesale
        ? selectedProduct.wholesalePrice || selectedProduct.price
        : selectedProduct.price;

      form.setValue(`items.${index}.priceAtSale`, priceValue);

      // Check for active event discounts for this product
      const eventDiscount = activeEventDiscounts.find((discount) =>
        discount.products.some((p) => p.product.id === productId)
      );

      if (eventDiscount) {
        // Apply event discount automatically
        const discountPercentage = Number(eventDiscount.discountPercentage);
        form.setValue(`items.${index}.discount`, discountPercentage.toString());

        toast.success(
          `Diskon event "${eventDiscount.name}" (${discountPercentage}%) diterapkan pada produk "${selectedProduct.name}"`
        );
      } else {
        // Clear any existing discount if no event discount applies
        form.setValue(`items.${index}.discount`, "0");
      }

      // Force recalculation of total immediately
      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        currentItems[index].priceAtSale = priceValue;
        // Update discount if event discount was applied
        if (eventDiscount) {
          currentItems[index].discount = Number(
            eventDiscount.discountPercentage
          ).toString();
        } else {
          currentItems[index].discount = "0";
        }
      }

      const total = currentItems.reduce(
        (sum: number, item: SaleFormValues["items"][number]) => {
          const quantity = item?.quantity ?? 0;
          const price = item?.priceAtSale ?? 0;
          const discountRate = parseFloat(item?.discount || "0") / 100;

          // Apply discount to price
          const discountAmount = price * discountRate;
          const priceAfterDiscount = price - discountAmount;

          return sum + quantity * priceAfterDiscount;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);

      // Show stock warning if stock is low (5 or less)
      if (selectedProduct.stock <= 5) {
        toast.warning(
          `Perhatian: Stok produk "${selectedProduct.name}" tinggal ${selectedProduct.stock} unit.`
        );
      }
    }
  };

  // Handle form submission with stock validation
  const onSubmit = async (values: SaleFormValues) => {
    // Check subscription limits before submission
    if (!canCreateTransaction) {
      toast.error(
        transactionMessage || "Batas transaksi tercapai untuk paket Anda."
      );
      return;
    }

    // Manually validate all fields
    const isValid = await form.trigger();

    // If validation fails, show error messages
    if (!isValid) {
      const errors = form.formState.errors;

      // Check for product selection errors
      if (errors.items) {
        toast.error("Minimal satu produk harus dipilih");
      }

      // Check for specific item errors
      values.items.forEach((item, index) => {
        if (!item.productId) {
          toast.error(`Item #${index + 1}: Produk wajib dipilih`);
        }
        if (!item.quantity || item.quantity <= 0) {
          toast.error(`Item #${index + 1}: Jumlah harus lebih dari 0`);
        }
        if (!item.priceAtSale || item.priceAtSale <= 0) {
          toast.error(`Item #${index + 1}: Harga jual harus positif`);
        }
      });

      return; // Stop submission if there are errors
    }

    // Additional stock validation before submission (only for non-draft)
    if (!values.isDraft) {
      for (let i = 0; i < values.items.length; i++) {
        const item = values.items[i];
        const product = products.find((p) => p.id === item.productId);

        if (product) {
          if (product.stock <= 0) {
            toast.error(
              `Item #${i + 1}: Produk "${product.name}" sedang habis stok.`
            );
            return;
          }

          if (item.quantity > product.stock) {
            toast.error(
              `Item #${i + 1}: Jumlah (${item.quantity}) melebihi stok tersedia (${product.stock}) untuk produk "${product.name}".`
            );
            return;
          }
        }
      }
    }

    startTransition(async () => {
      try {
        // Generate auto values if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        // If transaction number is empty, generate one
        if (!values.transactionNumber) {
          const trxResult = await getNextTransactionNumber("TRX");
          if (trxResult.success) {
            autoTransactionNumber = trxResult.nextNumber;
          }
        }

        // If invoice reference is empty, generate one
        if (!values.invoiceRef) {
          const invResult = await getNextInvoiceNumber();
          if (invResult.success) {
            autoInvoiceRef = invResult.nextNumber;
          }
        }

        // Extract the fields for the sale submission (only fields in base SaleSchema)
        // Process items to include discount information
        const processedItems = values.items.map((item) => {
          const discountPercentage = parseFloat(item.discount || "0");
          const discountAmount = (item.priceAtSale * discountPercentage) / 100;

          // Find the event discount that was applied to this product
          const eventDiscount = activeEventDiscounts.find((discount) =>
            discount.products.some((p) => p.product.id === item.productId)
          );

          // Only set event discount info if the discount percentage matches the event discount
          // and there's actually a discount being applied
          const isEventDiscountApplied =
            eventDiscount &&
            discountPercentage > 0 &&
            Math.abs(
              discountPercentage - Number(eventDiscount.discountPercentage)
            ) < 0.01;

          return {
            ...item,
            discountPercentage:
              discountPercentage > 0 ? discountPercentage : undefined,
            discountAmount: discountAmount > 0 ? discountAmount : undefined,
            eventDiscountId: isEventDiscountApplied
              ? eventDiscount.id
              : undefined,
            eventDiscountName: isEventDiscountApplied
              ? eventDiscount.name
              : undefined,
          };
        });

        const saleData = {
          items: processedItems,
          totalAmount: values.totalAmount,
          transactionNumber: autoTransactionNumber,
          invoiceRef: autoInvoiceRef,
          isDraft: values.isDraft || false,
          // Customer relationship
          customerId: values.customerId,
          // Additional fields
          customerRefNumber: values.customerRefNumber,
          shippingAddress: values.shippingAddress,
          paymentDueDate: values.paymentDueDate,
          paymentTerms: values.paymentTerms,
          warehouse: values.warehouse,
          tags: values.tags,
          memo: values.memo,
          lampiran: values.lampiran,
          priceIncludesTax: values.priceIncludesTax,
        };

        const result = await addSale(saleData);
        if (result.success) {
          if (values.isDraft) {
            toast.success("Penjualan berhasil disimpan sebagai draft!");
          } else {
            toast.success(result.success);
          }
          form.reset(); // Reset form on success
          // Redirect after a short delay
          router.push("/dashboard/sales");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Save as draft to database
  const handleSaveAsDraft = () => {
    startTransition(async () => {
      try {
        // Get current form values
        const values = form.getValues();

        // Generate transaction number and invoice reference if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        if (!autoTransactionNumber) {
          const transactionResult = await getNextTransactionNumber("TRX");
          if (transactionResult.success) {
            autoTransactionNumber = transactionResult.nextNumber;
          }
        }

        if (!autoInvoiceRef) {
          const invoiceResult = await getNextInvoiceNumber();
          if (invoiceResult.success) {
            autoInvoiceRef = invoiceResult.nextNumber;
          }
        }

        // Extract the fields for the sale submission (only fields in base SaleSchema)
        // Process items to include discount information
        const processedItems = values.items.map((item) => {
          const discountPercentage = parseFloat(item.discount || "0");
          const discountAmount = (item.priceAtSale * discountPercentage) / 100;

          // Find the event discount that was applied to this product
          const eventDiscount = activeEventDiscounts.find((discount) =>
            discount.products.some((p) => p.product.id === item.productId)
          );

          return {
            ...item,
            discountPercentage:
              discountPercentage > 0 ? discountPercentage : undefined,
            discountAmount: discountAmount > 0 ? discountAmount : undefined,
            eventDiscountId: eventDiscount?.id || undefined,
            eventDiscountName: eventDiscount?.name || undefined,
          };
        });

        const saleData = {
          items: processedItems,
          totalAmount: values.totalAmount,
          transactionNumber: autoTransactionNumber,
          invoiceRef: autoInvoiceRef,
          isDraft: true, // Set to true when saving as draft
          // Customer relationship
          customerId: values.customerId,
          // Additional fields
          customerRefNumber: values.customerRefNumber,
          shippingAddress: values.shippingAddress,
          paymentDueDate: values.paymentDueDate,
          paymentTerms: values.paymentTerms,
          warehouse: values.warehouse,
          tags: values.tags,
          memo: values.memo,
          lampiran: values.lampiran,
          priceIncludesTax: values.priceIncludesTax,
        };

        const result = await addSale(saleData);
        if (result.success) {
          toast.success("Penjualan berhasil disimpan sebagai draft!");
          form.reset(); // Reset form on success
          // Redirect after a short delay
          router.push("/dashboard/sales");
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Tambah Penjualan Baru</h1>
          <p className="text-muted-foreground">
            Catat transaksi penjualan baru dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button variant="outline" asChild className="gap-2 hidden md:flex">
          <Link href="/dashboard/sales">
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>

      {/* Subscription Limit Warning */}
      {transactionLimit && currentTransactionUsage !== undefined && (
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950 mb-6">
          <CardContent className="pt-0">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
              <div className="space-y-1">
                <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Batas Transaksi Bulanan
                </p>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  Anda telah menggunakan {currentTransactionUsage} dari{" "}
                  {transactionLimit} transaksi yang tersedia bulan ini.
                  {transactionLimit - currentTransactionUsage <= 5 && (
                    <span className="font-medium">
                      {" "}
                      Sisa {transactionLimit - currentTransactionUsage}{" "}
                      transaksi lagi.
                    </span>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stock Status Card - Only show when there are issues (no products or all out of stock) */}
      {availableCount === 0 && outOfStockCount > 0 && (
        <StockStatusCard
          availableCount={availableCount}
          outOfStockCount={outOfStockCount}
          totalProducts={availableCount + outOfStockCount}
        />
      )}

      <Form {...form}>
        <form
          onSubmit={(e) => {
            e.preventDefault();

            // Trigger validation on all fields
            form.trigger().then((isValid) => {
              if (!isValid) {
                // Show toast messages for validation errors
                const errors = form.formState.errors;

                // Check for product selection errors
                if (errors.items) {
                  toast.error("Minimal satu produk harus dipilih");
                }

                // Check each item for validation
                items.forEach((item, index) => {
                  if (!item.productId) {
                    toast.error(`Item #${index + 1}: Produk wajib dipilih`);
                    // Set error on the field
                    form.setError(`items.${index}.productId`, {
                      type: "manual",
                      message: "Produk wajib dipilih",
                    });
                  }
                  if (!item.quantity || item.quantity <= 0) {
                    toast.error(
                      `Item #${index + 1}: Jumlah harus lebih dari 0`
                    );
                    // Set error on the field
                    form.setError(`items.${index}.quantity`, {
                      type: "manual",
                      message: "Jumlah harus lebih dari 0",
                    });
                  }
                  if (!item.priceAtSale || item.priceAtSale <= 0) {
                    toast.error(`Item #${index + 1}: Harga jual harus positif`);
                    // Set error on the field
                    form.setError(`items.${index}.priceAtSale`, {
                      type: "manual",
                      message: "Harga jual harus positif",
                    });
                  }
                });

                return;
              }

              // If no errors, proceed with form submission
              form.handleSubmit(onSubmit)(e);
            });
          }}
        >
          <div className="w-full">
            <CombinedSaleForm
              control={form.control}
              isPending={isPending}
              products={products}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
              setValue={form.setValue}
              trigger={form.trigger}
              activeEventDiscounts={activeEventDiscounts}
            />
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-between md:justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              disabled={isPending}
              className="cursor-pointer"
              onClick={() => handleNavigation("/dashboard/sales")}
            >
              Batal
            </Button>
            <Button
              type="button"
              variant="secondary"
              disabled={isPending}
              className="gap-2"
              onClick={handleSaveAsDraft}
            >
              <Save className="h-4 w-4" />
              <span>
                <span className="hidden md:inline">Simpan ke </span>Draft
              </span>
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="gap-2 cursor-pointer"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span>Simpan Penjualan</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
            <AlertDialogDescription>
              Anda memiliki perubahan yang belum tersimpan. Jika Anda
              meninggalkan halaman ini, perubahan Anda akan hilang.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
              <AlertDialogCancel
                onClick={cancelNavigation}
                className="cursor-pointer w-full"
              >
                Kembali ke Form
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmNavigation}
                className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
              >
                Buang Perubahan
              </AlertDialogAction>
              <Button
                type="button"
                variant="default"
                className="cursor-pointer w-full"
                onClick={() => {
                  setShowExitDialog(false);
                  handleSaveAsDraft();
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                Simpan ke Draft
              </Button>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default EnhancedSalePage;
